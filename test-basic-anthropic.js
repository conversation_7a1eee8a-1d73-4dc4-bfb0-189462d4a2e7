#!/usr/bin/env node

/**
 * 基础 Anthropic API 测试
 * 测试基本的消息发送功能
 */

import fetch from 'node-fetch';

// API密钥
const API_KEY = '************************************************************************************************************';

/**
 * 测试基本消息发送
 */
async function testBasicMessage() {
  console.log('=== 测试基本消息发送 ===\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 500,
    messages: [
      {
        role: 'user',
        content: 'Hello! Please tell me a short joke.'
      }
    ],
    temperature: 0.7
  };

  console.log('发送请求:', JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 请求成功');
      console.log('响应数据:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ 请求失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 测试包含空消息的情况
 */
async function testEmptyContentMessage() {
  console.log('\n=== 测试包含空消息的情况 ===\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 500,
    messages: [
      {
        role: 'user',
        content: 'Hello'
      },
      {
        role: 'assistant',
        content: '' // 空内容，应该被过滤
      },
      {
        role: 'user',
        content: 'Are you there?'
      }
    ],
    temperature: 0.7
  };

  console.log('发送包含空消息的请求:', JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 空消息处理成功');
      console.log('响应数据:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ 空消息处理失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 测试带system消息的情况
 */
async function testSystemMessage() {
  console.log('\n=== 测试带system消息的情况 ===\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 500,
    messages: [
      {
        role: 'system',
        content: 'You are a helpful assistant that always responds cheerfully.'
      },
      {
        role: 'user',
        content: 'Hello, how are you today?'
      }
    ],
    temperature: 0.7
  };

  console.log('发送带system消息的请求:', JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ System消息处理成功');
      console.log('响应数据:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ System消息处理失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 测试服务器连接
 */
async function testServerConnection() {
  console.log('=== 测试服务器连接 ===\n');

  try {
    const response = await fetch('http://127.0.0.1:3001/health', {
      method: 'GET',
      timeout: 5000
    });

    if (response.ok) {
      console.log('✅ 服务器连接正常');
      return true;
    } else {
      console.log('❌ 服务器响应异常:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ 服务器连接失败:', error.message);
    console.log('请确保 MCP 服务器在 http://127.0.0.1:3001 上运行');
    return false;
  }
}

/**
 * 测试模型列表
 */
async function testModelsList() {
  console.log('\n=== 测试模型列表 ===\n');

  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/models', {
      method: 'GET',
      headers: {
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01',
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 模型列表获取成功');
      console.log('可用模型数量:', data.data?.length || 0);
      if (data.data && data.data.length > 0) {
        console.log('首个模型:', data.data[0].id);
      }
      return true;
    } else {
      const errorText = await response.text();
      console.log('❌ 模型列表获取失败:', errorText);
      return false;
    }
  } catch (error) {
    console.log('❌ 模型列表请求错误:', error.message);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始基础 Anthropic API 测试...\n');

  // 首先测试服务器连接
  const serverOk = await testServerConnection();
  if (!serverOk) {
    console.log('\n服务器连接失败，停止测试');
    return;
  }

  await new Promise(resolve => setTimeout(resolve, 1000));

  // 测试模型列表
  const modelsOk = await testModelsList();
  if (!modelsOk) {
    console.log('\n模型列表获取失败，跳过消息测试');
  } else {
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试消息发送
    await testBasicMessage();
    await new Promise(resolve => setTimeout(resolve, 2000));

    await testEmptyContentMessage();
    await new Promise(resolve => setTimeout(resolve, 2000));

    await testSystemMessage();
  }

  console.log('\n所有测试完成！');
}

// 运行测试
runAllTests().catch(console.error);

export {
  testBasicMessage,
  testEmptyContentMessage,
  testSystemMessage,
  runAllTests
};
