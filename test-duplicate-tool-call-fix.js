#!/usr/bin/env node

/**
 * 测试重复工具调用卡片修复
 */

import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

/**
 * 模拟前端消息解析逻辑
 */
function simulateFrontendMessageParsing(messageContent) {
  console.log('🔍 模拟前端解析消息内容...\n');
  
  // 提取所有工具调用JSON
  const toolCalls = [];
  let searchIndex = 0;
  
  while (true) {
    const startIndex = messageContent.indexOf('{"type":"tool_calls"', searchIndex);
    if (startIndex === -1) break;
    
    // 查找匹配的右花括号
    let braceCount = 0;
    let endIndex = startIndex;
    
    for (let i = startIndex; i < messageContent.length; i++) {
      if (messageContent[i] === '{') braceCount++;
      if (messageContent[i] === '}') braceCount--;
      if (braceCount === 0) {
        endIndex = i;
        break;
      }
    }
    
    if (braceCount === 0) {
      const jsonStr = messageContent.substring(startIndex, endIndex + 1);
      
      try {
        const toolCallObj = JSON.parse(jsonStr);
        
        if (toolCallObj && toolCallObj.tool_calls && Array.isArray(toolCallObj.tool_calls)) {
          // 检查是否包含结果
          const hasResults = toolCallObj.tool_calls.some(call => 
            call.result !== undefined || call.error !== undefined
          );
          
          if (hasResults) {
            toolCalls.push({
              type: 'with_results',
              data: toolCallObj,
              toolName: toolCallObj.tool_calls[0]?.name,
              hasResult: true
            });
            console.log('✅ 发现包含结果的工具调用:', toolCallObj.tool_calls[0]?.name);
          } else {
            console.log('⏭️  忽略不包含结果的工具调用定义:', toolCallObj.tool_calls[0]?.name);
          }
        }
      } catch (e) {
        console.error('解析JSON失败:', e);
      }
    }
    
    searchIndex = endIndex + 1;
  }
  
  console.log(`\n📊 解析结果: 找到 ${toolCalls.length} 个有效的工具调用卡片`);
  return toolCalls;
}

/**
 * 测试工具调用流程
 */
async function testToolCallFlow() {
  console.log('🧪 测试工具调用流程（检查重复卡片问题）...\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: '请帮我创建一个文件 /Users/<USER>/Desktop/duplicate_test.txt，内容是 "Testing duplicate tool call fix"'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    temperature: 0.7
  };

  console.log('1️⃣ 发送请求给 Claude...');
  
  try {
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Claude 响应成功\n');
      
      // 模拟前端处理流程
      console.log('2️⃣ 模拟前端处理 Claude 响应...');
      
      // 这里模拟 MCPClient.processStreamQuery 的处理
      if (data.content && Array.isArray(data.content)) {
        for (const content of data.content) {
          if (content.type === 'tool_use') {
            console.log('🔧 发现工具调用，开始执行...');
            console.log('工具名称:', content.name);
            console.log('参数:', JSON.stringify(content.input, null, 2));
            
            // 模拟工具执行
            try {
              const toolResponse = await fetch('http://127.0.0.1:3001/mcp/tools/call', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  name: content.name,
                  arguments: content.input
                })
              });
              
              if (toolResponse.ok) {
                const toolResult = await toolResponse.json();
                console.log('✅ 工具执行成功');
                
                // 提取结果文本
                let extractedResult = '';
                if (toolResult.result && toolResult.result.content && Array.isArray(toolResult.result.content)) {
                  extractedResult = toolResult.result.content
                    .filter(item => item.type === 'text')
                    .map(item => item.text)
                    .join('\n');
                }
                
                // 生成包含结果的工具调用JSON（这是前端会解析的）
                const finalToolCallJson = JSON.stringify({
                  type: 'tool_calls',
                  tool_calls: [{
                    name: content.name,
                    arguments: content.input,
                    result: extractedResult,
                    success: true
                  }]
                });
                
                console.log('\n3️⃣ 生成的最终工具调用JSON:');
                console.log(finalToolCallJson);
                
                // 模拟完整的消息内容（包含初始定义和最终结果）
                const simulatedMessageContent = `
我将使用 write_file 工具将5个对联写入桌面的txt文件中：

{"type":"tool_calls","tool_calls":[{"name":"write_file","arguments":{"path":"/Users/<USER>/Desktop/duplicate_test.txt","content":"Testing duplicate tool call fix"}}]}

${finalToolCallJson}

文件已成功创建！
                `.trim();
                
                console.log('\n4️⃣ 模拟前端解析消息内容:');
                const parsedToolCalls = simulateFrontendMessageParsing(simulatedMessageContent);
                
                if (parsedToolCalls.length === 1) {
                  console.log('\n🎉 修复成功！只显示一个工具调用卡片');
                } else if (parsedToolCalls.length > 1) {
                  console.log('\n❌ 修复失败，仍然有重复的工具调用卡片');
                } else {
                  console.log('\n⚠️  没有找到工具调用卡片');
                }
                
              } else {
                console.log('❌ 工具执行失败');
              }
            } catch (toolError) {
              console.error('工具执行错误:', toolError);
            }
          }
        }
      }
      
    } else {
      const errorText = await response.text();
      console.log('❌ Claude 响应失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 请求错误:', error.message);
  }
}

/**
 * 测试消息解析逻辑
 */
function testMessageParsingLogic() {
  console.log('\n\n🧪 测试消息解析逻辑...\n');
  
  // 模拟包含重复工具调用的消息
  const messageWithDuplicates = `
我将使用 write_file 工具创建文件：

{"type":"tool_calls","tool_calls":[{"name":"write_file","arguments":{"path":"/Users/<USER>/Desktop/test.txt","content":"Hello World"}}]}

{"type":"tool_calls","tool_calls":[{"name":"write_file","arguments":{"path":"/Users/<USER>/Desktop/test.txt","content":"Hello World"},"result":"Successfully wrote to /Users/<USER>/Desktop/test.txt","success":true}]}

文件创建完成！
  `.trim();
  
  console.log('测试消息内容:');
  console.log(messageWithDuplicates);
  
  console.log('\n解析结果:');
  const parsedToolCalls = simulateFrontendMessageParsing(messageWithDuplicates);
  
  if (parsedToolCalls.length === 1) {
    console.log('✅ 解析正确，只保留了包含结果的工具调用');
  } else {
    console.log('❌ 解析错误，仍然有重复的工具调用');
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始测试重复工具调用卡片修复...\n');
  
  testMessageParsingLogic();
  await testToolCallFlow();
  
  console.log('\n🎯 测试总结:');
  console.log('1. 前端现在只显示包含结果的工具调用卡片');
  console.log('2. 忽略中间的工具调用定义阶段');
  console.log('3. 避免了重复的工具调用卡片问题');
  
  console.log('\n所有测试完成！');
}

runAllTests().catch(console.error);
