import OpenAI from 'openai';
import { getProviderById } from './ModelProviders';

interface LLMServiceOptions {
  apiKey?: string;
  baseUrl?: string;
  model?: string;
  providerId?: string;
}

export class LLMService {
  private client: OpenAI;
  private model: string;
  private baseURL: string;
  private providerId: string;
  private apiKey: string;
  private tools: Array<{name: string, description: string, inputSchema: any}> = [];

  constructor(options?: LLMServiceOptions) {
    // 获取API密钥，优先使用传入的选项
    this.apiKey = options?.apiKey || import.meta.env.VITE_API_KEY;

    if (!this.apiKey) {
      console.error('API密钥未设置');
      throw new Error('API密钥未设置，请在设置中配置API密钥');
    }

    // 确定提供商ID
    this.providerId = options?.providerId || localStorage.getItem('providerId') || import.meta.env.VITE_MODEL_PROVIDER || 'openai';

    // 获取提供商信息
    const provider = getProviderById(this.providerId);

    // 获取baseURL配置
    if (this.providerId === 'custom') {
      // 自定义提供商使用传入的baseUrl或localStorage中的customBaseUrl
      this.baseURL = options?.baseUrl || localStorage.getItem('customBaseUrl') || '';
    } else {
      // 其他提供商使用传入的baseUrl或提供商默认baseUrl
      this.baseURL = options?.baseUrl || (provider?.baseUrl || 'https://api.openai.com/v1');
    }

    console.log(`使用API基础URL: ${this.baseURL}`);

    // 初始化OpenAI实例
    this.client = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.baseURL,
      dangerouslyAllowBrowser: true // 在浏览器环境中使用
    });

    // 使用指定的模型或默认值
    if (this.providerId === 'custom') {
      this.model = options?.model || localStorage.getItem('customModelId') || 'gpt-3.5-turbo';
    } else {
      this.model = options?.model || localStorage.getItem('modelId') || import.meta.env.VITE_OPENAI_MODEL || 'gpt-3.5-turbo';
    }

    console.log(`使用模型: ${this.model}`);
  }

  /**
   * 获取当前使用的基础URL
   */
  getBaseURL(): string {
    return this.baseURL;
  }

  /**
   * 获取当前使用的模型
   */
  getModel(): string {
    return this.model;
  }

  /**
   * 获取当前使用的提供商ID
   */
  getProviderId(): string {
    return this.providerId;
  }

  /**
   * 设置新的API密钥
   * @param apiKey 新的API密钥
   */
  setApiKey(apiKey: string): void {
    if (!apiKey) {
      console.error('API密钥未设置');
      return;
    }

    // 更新实例的apiKey属性
    this.apiKey = apiKey;

    // 重新创建OpenAI实例，使用新的API密钥
    this.client = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.baseURL,
      dangerouslyAllowBrowser: true
    });

    console.log('API密钥已更新');
  }

  /**
   * 发送消息到LLM服务并获取回复
   * @param messages 消息历史
   * @param tools 可用工具列表
   */
  async sendMessage(
    messages: Array<{role: 'user' | 'assistant' | 'system', content: string}>,
    tools?: Array<{name: string, description: string, inputSchema: any}>
  ): Promise<string> {
    // 优先使用存储的工具列表，如果传入了tools参数则使用传入的
    const toolsToUse = tools || (this.tools.length > 0 ? this.tools : undefined);

    try {
      // 如果使用 Anthropic API
      if (this.providerId === 'anthropic') {
        return await this.sendAnthropicMessage(messages, toolsToUse);
      }

      // 其他提供商使用 OpenAI 格式
      // 准备工具定义
      const formattedTools = toolsToUse?.map(tool => ({
        type: 'function' as const,
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.inputSchema || {
            type: 'object',
            properties: {},
            required: []
          }
        }
      }));

      // 调用Chat Completions API
      const params: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
        model: this.model,
        messages,
        temperature: 0.7,
        max_tokens: 1000
      };

      // 只有当formattedTools有值且不为空数组时才添加tools参数
      // 特别是对DeepSeek API，空tools数组会导致400错误
      if (formattedTools && formattedTools.length > 0) {
        params.tools = formattedTools;
      }

      const response = await this.client.chat.completions.create(params);

      // 获取响应
      const responseMessage = response.choices[0].message;

      // 检查是否有工具调用
      if (responseMessage.tool_calls && responseMessage.tool_calls.length > 0) {
        return JSON.stringify({
          type: 'tool_calls',
          tool_calls: responseMessage.tool_calls.map(call => ({
            name: call.function.name,
            arguments: JSON.parse(call.function.arguments)
          }))
        });
      }

      return responseMessage.content || '无响应';
    } catch (error) {
      console.error('调用API出错:', error);
      throw new Error('调用API出错: ' + (error as Error).message);
    }
  }

  /**
   * 清理和验证 Anthropic 消息格式
   * @param messages 原始消息列表
   * @returns 清理后的消息列表
   */
  private cleanAnthropicMessages(
    messages: Array<{role: 'user' | 'assistant' | 'system', content: string}>
  ): Array<{role: 'user' | 'assistant', content: string}> {
    console.log('开始清理Anthropic消息，原始消息数量:', messages.length);

    if (!messages || !Array.isArray(messages)) {
      throw new Error('消息列表无效：messages必须是数组');
    }

    if (messages.length === 0) {
      throw new Error('消息列表不能为空');
    }

    // 第一步：基础验证和清理
    const validMessages = [];

    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];

      // 验证消息结构
      if (!msg || typeof msg !== 'object') {
        console.warn(`跳过无效消息 [${i}]: 不是对象`);
        continue;
      }

      if (!msg.role || typeof msg.role !== 'string') {
        console.warn(`跳过无效消息 [${i}]: 缺少或无效的role`);
        continue;
      }

      if (!msg.content || typeof msg.content !== 'string') {
        console.warn(`跳过无效消息 [${i}]: 缺少或无效的content`);
        continue;
      }

      // 清理内容
      const cleanedContent = msg.content.trim();
      if (cleanedContent.length === 0) {
        console.warn(`跳过空内容消息 [${i}]: role=${msg.role}`);
        continue;
      }

      // 验证角色（只保留非system消息）
      if (msg.role === 'system') {
        continue; // system消息在外部单独处理
      }

      if (!['user', 'assistant'].includes(msg.role)) {
        console.warn(`跳过无效角色消息 [${i}]: role=${msg.role}`);
        continue;
      }

      validMessages.push({
        role: msg.role as 'user' | 'assistant',
        content: cleanedContent
      });
    }

    if (validMessages.length === 0) {
      throw new Error('没有有效的对话消息（user/assistant），至少需要一条用户消息');
    }

    // 第二步：合并连续的相同角色消息
    const mergedMessages: Array<{role: 'user' | 'assistant', content: string}> = [];

    for (const msg of validMessages) {
      if (mergedMessages.length === 0) {
        mergedMessages.push({ ...msg });
      } else {
        const lastMsg = mergedMessages[mergedMessages.length - 1];
        if (lastMsg.role === msg.role) {
          // 合并相同角色的消息
          lastMsg.content += '\n\n' + msg.content;
          console.log(`合并相同角色消息: ${msg.role}`);
        } else {
          mergedMessages.push({ ...msg });
        }
      }
    }

    // 第三步：确保消息序列符合Anthropic要求
    // 1. 移除末尾的assistant消息（Anthropic要求最后一条消息必须是user）
    while (mergedMessages.length > 0 &&
           mergedMessages[mergedMessages.length - 1].role === 'assistant') {
      console.log('移除末尾的assistant消息');
      mergedMessages.pop();
    }

    // 2. 确保至少有一条消息
    if (mergedMessages.length === 0) {
      throw new Error('清理后没有有效的消息，请检查消息历史');
    }

    // 3. 确保第一条消息是用户消息
    if (mergedMessages[0].role !== 'user') {
      throw new Error('第一条消息必须是用户消息');
    }

    // 4. 确保最后一条消息是用户消息
    if (mergedMessages[mergedMessages.length - 1].role !== 'user') {
      throw new Error('最后一条消息必须是用户消息');
    }

    // 5. 验证消息交替模式（user -> assistant -> user -> ...）
    for (let i = 0; i < mergedMessages.length; i++) {
      const expectedRole = i % 2 === 0 ? 'user' : 'assistant';
      if (mergedMessages[i].role !== expectedRole) {
        console.warn(`消息序列不符合交替模式，位置 ${i}: 期望 ${expectedRole}，实际 ${mergedMessages[i].role}`);
        // 不抛出错误，只记录警告，因为Anthropic可以处理一些不规则的序列
      }
    }

    console.log(`消息清理完成: 原始 ${messages.length} 条 -> 有效 ${validMessages.length} 条 -> 最终 ${mergedMessages.length} 条`);

    return mergedMessages;
  }

  /**
   * 发送消息到 Anthropic API
   * @param messages 消息历史
   * @param tools 可用工具列表
   */
  private async sendAnthropicMessage(
    messages: Array<{role: 'user' | 'assistant' | 'system', content: string}>,
    tools?: Array<{name: string, description: string, inputSchema: any}>
  ): Promise<string> {
    try {
      console.log('使用 Anthropic API 发送消息');
      console.log('原始消息数量:', messages.length);

      // 清理和验证消息格式
      const anthropicMessages = this.cleanAnthropicMessages(messages);

      // 提取和合并 system 消息
      const systemMessages = messages.filter(msg => msg.role === 'system');
      let systemMessage = '';
      if (systemMessages.length > 0) {
        systemMessage = systemMessages
          .map(msg => msg.content?.trim())
          .filter(content => content && content.length > 0)
          .join('\n\n');
      }

      console.log('Anthropic 清理后消息数量:', anthropicMessages.length);
      console.log('Anthropic 消息列表:', anthropicMessages);
      console.log('System 消息长度:', systemMessage.length);

      // 转换工具格式
      const anthropicTools = tools?.map(tool => ({
        name: tool.name,
        description: tool.description,
        input_schema: tool.inputSchema || {
          type: 'object',
          properties: {},
          required: []
        }
      }));

      // 构建请求体
      const requestBody: any = {
        model: this.model,
        max_tokens: 1000,
        messages: anthropicMessages,
        temperature: 0.7
      };

      // 添加 system prompt
      if (systemMessage && systemMessage.trim().length > 0) {
        requestBody.system = systemMessage.trim();
        console.log('添加system消息到请求体');
      }

      // 添加工具
      if (anthropicTools && anthropicTools.length > 0) {
        requestBody.tools = anthropicTools;
      }

      console.log('Anthropic 请求体:', JSON.stringify(requestBody, null, 2));

      // 发送请求到代理路由
      const response = await fetch('/api/anthropic/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Anthropic API 错误: ${response.status}`, errorText);
        throw new Error(`Anthropic API 错误: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Anthropic API 响应:', data);

      // 解析响应
      if (data.content && data.content.length > 0) {
        // 处理多个内容块
        let textContent = '';
        let toolCalls = [];

        for (const content of data.content) {
          // 检查是否有工具调用
          if (content.type === 'tool_use') {
            console.log('非流式工具调用:', content);
            toolCalls.push({
              name: content.name,
              arguments: content.input || {}
            });
          }

          // 收集文本内容
          if (content.type === 'text') {
            textContent += content.text || '';
          }
        }

        // 如果有工具调用，返回工具调用定义
        // 实际执行由 MCPClient.ts 统一处理
        if (toolCalls.length > 0) {
          const toolResponse = JSON.stringify({
            type: 'tool_calls',
            tool_calls: toolCalls
          });
          console.log('非流式工具调用定义:', toolResponse);
          return toolResponse;
        }

        // 返回文本内容
        if (textContent) {
          return textContent;
        }
      }

      return '无响应';
    } catch (error) {
      console.error('Anthropic API 调用失败:', error);
      throw error;
    }
  }

  /**
   * 发送消息到LLM服务并获取流式回复
   * @param messages 消息历史
   * @param tools 可用工具列表
   * @param onChunk 接收数据块的回调函数
   */
  async sendStreamMessage(
    messages: Array<{role: 'user' | 'assistant' | 'system', content: string}>,
    onChunk: (chunk: string) => void,
    tools?: Array<{name: string, description: string, inputSchema: any}>
  ): Promise<string> {
    // 优先使用存储的工具列表，如果传入了tools参数则使用传入的
    const toolsToUse = tools || (this.tools.length > 0 ? this.tools : undefined);

    try {
      // 如果使用 Anthropic API
      if (this.providerId === 'anthropic') {
        return await this.sendAnthropicStreamMessage(messages, onChunk, toolsToUse);
      }

      // 其他提供商使用 OpenAI 格式
      // 准备工具定义
      const formattedTools = toolsToUse?.map(tool => ({
        type: 'function' as const,
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.inputSchema || {
            type: 'object',
            properties: {},
            required: []
          }
        }
      }));

      // 调用Chat Completions API，启用流式响应
      const params: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
        model: this.model,
        messages,
        temperature: 0.7,
        max_tokens: 1000,
        stream: true,
      };

      // 只有当formattedTools有值且不为空数组时才添加tools参数
      // 特别是对DeepSeek API，空tools数组会导致400错误
      if (formattedTools && formattedTools.length > 0) {
        params.tools = formattedTools;
      }

      const stream = await this.client.chat.completions.create(params);

      let fullResponse = '';
      let toolCallsData: any[] = [];
      let isToolCall = false;

      // 处理流式响应
      for await (const chunk of stream) {
        // 获取当前块的内容
        const content = chunk.choices[0]?.delta?.content || '';
        const toolCalls = chunk.choices[0]?.delta?.tool_calls || [];

        // 如果有工具调用
        if (toolCalls.length > 0) {
          isToolCall = true;

          // 将工具调用数据添加到集合中
          toolCalls.forEach(toolCall => {
            if (!toolCallsData.some(t => t.index === toolCall.index)) {
              toolCallsData.push({
                index: toolCall.index,
                id: toolCall.id,
                type: toolCall.type,
                function: {
                  name: toolCall.function?.name || '',
                  arguments: toolCall.function?.arguments || ''
                }
              });
            } else {
              // 更新现有工具调用的参数
              const existingTool = toolCallsData.find(t => t.index === toolCall.index);
              if (existingTool && toolCall.function?.arguments) {
                existingTool.function.arguments += toolCall.function.arguments;
              }
            }
          });
        }

        // 如果有文本内容，添加到完整响应
        if (content) {
          fullResponse += content;
          onChunk(content);
        }
      }

      // 如果是工具调用，返回工具调用数据的JSON
      if (isToolCall) {
        // 格式化工具调用数据
        const formattedToolCalls = toolCallsData.map(call => {
          try {
            // 尝试解析JSON参数
            return {
              name: call.function.name,
              arguments: JSON.parse(call.function.arguments)
            };
          } catch (e) {
            // 如果解析失败，返回原始字符串
            return {
              name: call.function.name,
              arguments: call.function.arguments
            };
          }
        });

        const toolCallResponse = JSON.stringify({
          type: 'tool_calls',
          tool_calls: formattedToolCalls
        });

        // 将工具调用响应传递给回调
        onChunk(toolCallResponse);
        return toolCallResponse;
      }

      return fullResponse;
    } catch (error) {
      console.error('调用流式API出错:', error);
      const errorMessage = '调用API出错: ' + (error as Error).message;
      onChunk(errorMessage);
      throw new Error(errorMessage);
    }
  }

  /**
   * 发送流式消息到 Anthropic API
   * @param messages 消息历史
   * @param onChunk 接收数据块的回调函数
   * @param tools 可用工具列表
   */
  private async sendAnthropicStreamMessage(
    messages: Array<{role: 'user' | 'assistant' | 'system', content: string}>,
    onChunk: (chunk: string) => void,
    tools?: Array<{name: string, description: string, inputSchema: any}>
  ): Promise<string> {
    try {
      console.log('使用 Anthropic API 发送流式消息');
      console.log('原始流式消息数量:', messages.length);

      // 清理和验证消息格式
      const anthropicMessages = this.cleanAnthropicMessages(messages);

      // 提取和合并 system 消息
      const systemMessages = messages.filter(msg => msg.role === 'system');
      let systemMessage = '';
      if (systemMessages.length > 0) {
        systemMessage = systemMessages
          .map(msg => msg.content?.trim())
          .filter(content => content && content.length > 0)
          .join('\n\n');
      }

      console.log('Anthropic 清理后流式消息数量:', anthropicMessages.length);
      console.log('Anthropic 流式消息列表:', anthropicMessages);
      console.log('System 消息长度:', systemMessage.length);

      // 转换工具格式
      const anthropicTools = tools?.map(tool => ({
        name: tool.name,
        description: tool.description,
        input_schema: tool.inputSchema || {
          type: 'object',
          properties: {},
          required: []
        }
      }));

      // 构建请求体
      const requestBody: any = {
        model: this.model,
        max_tokens: 1000,
        messages: anthropicMessages,
        temperature: 0.7,
        stream: true
      };

      // 添加 system prompt
      if (systemMessage && systemMessage.trim().length > 0) {
        requestBody.system = systemMessage.trim();
        console.log('添加system消息到流式请求体');
      }

      // 添加工具
      if (anthropicTools && anthropicTools.length > 0) {
        requestBody.tools = anthropicTools;
      }

      console.log('Anthropic 流式请求体:', JSON.stringify(requestBody, null, 2));

      // 发送请求到代理路由
      const response = await fetch('/api/anthropic/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Anthropic API 错误: ${response.status}`, errorText);
        throw new Error(`Anthropic API 错误: ${response.status} - ${errorText}`);
      }

      // 处理流式响应
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      let fullResponse = '';
      const decoder = new TextDecoder();
      let currentToolCall: any = null;
      let isToolCall = false;

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                break;
              }

              try {
                const parsed = JSON.parse(data);
                console.log('Anthropic 流式事件:', parsed);

                // 处理文本内容
                if (parsed.type === 'content_block_delta' && parsed.delta?.text) {
                  const text = parsed.delta.text;
                  fullResponse += text;
                  onChunk(text);
                }

                // 处理工具调用开始
                if (parsed.type === 'content_block_start' && parsed.content_block?.type === 'tool_use') {
                  console.log('工具调用开始:', parsed.content_block);
                  currentToolCall = {
                    id: parsed.content_block.id,
                    name: parsed.content_block.name,
                    input: {} // 初始化为空对象
                  };
                  isToolCall = true;
                }

                // 处理工具调用参数增量
                if (parsed.type === 'content_block_delta' && parsed.delta?.type === 'input_json_delta') {
                  if (currentToolCall && parsed.delta.partial_json) {
                    console.log('工具参数增量:', parsed.delta.partial_json);
                    // 累积工具参数的JSON字符串
                    if (!currentToolCall.inputJson) {
                      currentToolCall.inputJson = '';
                    }
                    currentToolCall.inputJson += parsed.delta.partial_json;
                  }
                }

                // 处理工具调用结束
                if (parsed.type === 'content_block_stop') {
                  if (currentToolCall && isToolCall) {
                    console.log('工具调用结束:', currentToolCall);

                    // 解析完整的工具参数
                    try {
                      if (currentToolCall.inputJson) {
                        currentToolCall.input = JSON.parse(currentToolCall.inputJson);
                      }
                    } catch (e) {
                      console.error('解析工具参数JSON失败:', e);
                      currentToolCall.input = {};
                    }

                    // 只发送工具调用定义，不执行实际工具
                    // 实际执行由 MCPClient.ts 统一处理
                    const toolResponse = JSON.stringify({
                      type: 'tool_calls',
                      tool_calls: [{
                        name: currentToolCall.name,
                        arguments: currentToolCall.input
                      }]
                    });

                    console.log('发送工具调用定义:', toolResponse);
                    fullResponse = toolResponse;
                    onChunk(toolResponse);

                    // 重置状态
                    currentToolCall = null;
                    isToolCall = false;
                    break;
                  }
                }
              } catch (parseError) {
                console.warn('解析 SSE 数据失败:', parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      return fullResponse;
    } catch (error) {
      console.error('Anthropic 流式 API 调用失败:', error);
      const errorMessage = 'Anthropic API 错误: ' + (error as Error).message;
      onChunk(errorMessage);
      throw error;
    }
  }

  /**
   * 获取可用模型列表
   * @param provider 可选的提供商参数，用于覆盖实例的providerId
   * @returns 模型列表
   */
  async getModels(provider?: string): Promise<Array<{id: string, name: string, display_name?: string, created_at?: string}>> {
    try {
      const targetProvider = provider || this.providerId;

      // 如果使用Anthropic API
      if (targetProvider === 'anthropic') {
        console.log('获取Anthropic模型列表');

        // 使用代理路由获取Anthropic模型列表
        const response = await fetch('/api/anthropic/models', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': this.apiKey,
            'anthropic-version': '2023-06-01'
          }
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`获取Anthropic模型列表失败: ${response.status}`, errorText);
          throw new Error(`获取Anthropic模型列表失败: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('获取到Anthropic模型列表:', data);

        // 检查响应格式，Anthropic API返回的是 data.data 数组
        const models = data.data || data.models || [];

        if (!Array.isArray(models)) {
          console.error('Anthropic API返回的模型数据格式不正确:', data);
          throw new Error('模型数据格式不正确');
        }

        // 将Anthropic模型转换为标准格式
        return models.map((model: any) => ({
          id: model.id,
          name: model.display_name || model.id,
          display_name: model.display_name,
          created_at: model.created_at
        }));
      }
      // 如果使用OpenAI API
      else if (targetProvider !== 'custom' && this.client) {
        const response = await this.client.models.list();
        return response.data.map(model => ({
          id: model.id,
          name: model.id
        }));
      } else {
        // 如果是自定义提供商，返回空列表
        return [];
      }
    } catch (error) {
      console.error('获取模型列表失败:', error);
      throw error; // 重新抛出错误，让调用者处理
    }
  }

  /**
   * 更新工具列表
   * @param tools 新的工具列表
   */
  updateTools(tools: Array<{name: string, description: string, inputSchema: any}>): void {
    console.log(`LLMService: 更新工具列表，数量 ${tools.length}`);
    // 保存工具列表到实例变量中，以便在后续API调用中使用
    this.tools = [...tools];
  }


}